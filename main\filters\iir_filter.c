#include "iir_filter.h"

void iir_init(IIRFilter *f, float a0, float a1, float a2, float b1, float b2) {
    f->a0 = a0;
    f->a1 = a1;
    f->a2 = a2;
    f->b1 = b1;
    f->b2 = b2;
    f->x1 = f->x2 = 0.0f;
    f->y1 = f->y2 = 0.0f;
}

float iir_filter(IIRFilter *f, float x) {
    float y = f->a0 * x + f->a1 * f->x1 + f->a2 * f->x2 - f->b1 * f->y1 - f->b2 * f->y2;
    f->x2 = f->x1;
    f->x1 = x;
    f->y2 = f->y1;
    f->y1 = y;
    return y;
}