{"configurations": [{"name": "ESP-IDF", "compilerPath": "${config:idf.toolsPath}/tools/xtensa-esp32-elf/esp-14.2.0_20241119/xtensa-esp32-elf/bin/xtensa-esp32-elf-gcc.exe", "includePath": ["${workspaceFolder}/**", "${config:idf.espIdfPath}/components/**", "${config:idf.espIdfPathWin}/components/**", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/**"], "browse": {"path": ["${workspaceFolder}", "${config:idf.espIdfPath}/components", "${config:idf.espIdfPathWin}/components", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components"], "limitSymbolsToIncludedHeaders": false}, "defines": ["ESP_PLATFORM", "ESP32"], "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "gcc-x64", "compileCommands": ["${workspaceFolder}/build/compile_commands.json"]}], "version": 4}