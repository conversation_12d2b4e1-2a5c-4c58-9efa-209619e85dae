{"version": "1.2", "project_name": "softap_sta", "project_version": "1", "project_path": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta", "idf_path": "C:/Users/<USER>/esp/v5.4.1/esp-idf", "build_dir": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build", "config_file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/sdkconfig", "config_defaults": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/sdkconfig.defaults", "bootloader_elf": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/bootloader/bootloader.elf", "app_elf": "softap_sta.elf", "app_bin": "softap_sta.bin", "build_type": "flash_app", "git_revision": "v5.4.1", "target": "esp32", "rev": "0", "min_rev": "0", "max_rev": "399", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32-elf-", "c_compiler": "C:/Espressif/tools/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt/esp-mqtt/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/openthread/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ulp/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/Kconfig;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader/Kconfig.projbuild;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format/Kconfig.projbuild;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/Kconfig.projbuild;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/Kconfig.projbuild;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/Kconfig.projbuild;C:/Users/<USER>/Desktop/vscode/softap/softap_sta/main/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["app_trace", "app_update", "bootloader", "bootloader_support", "bt", "cmock", "console", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_security", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "esptool_py", "fatfs", "freertos", "hal", "heap", "http_parser", "idf_test", "ieee802154", "json", "log", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "perfmon", "protobuf-c", "protocomm", "pthread", "rt", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "xtensa", ""], "build_component_paths": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip", "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/main", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/openthread", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ulp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa", ""], "build_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/app_trace/libapp_trace.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace/app_trace.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace/app_trace_util.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace/host_file_io.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/app_update/libapp_update.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update/esp_ota_ops.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_common_loader.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_clock_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_mem.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_random.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_efuse.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/flash_encrypt.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/secure_boot.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_random_esp32.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/bootloader_utility.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/flash_partitions.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/esp_image_format.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/idf/bootloader_sha.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support/src/esp32/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt", "type": "CONFIG_ONLY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/cmock/libcmock.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/console/libconsole.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/commands.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/esp_console_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/split_argv.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/linenoise/linenoise.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/esp_console_repl_chip.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_cmd.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_date.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_dbl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_dstr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_end.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_file.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_hashtable.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_int.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_lit.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_rem.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_rex.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_str.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/arg_utils.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console/argtable3/argtable3.c"], "include_dirs": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/cxx/libcxx.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx/cxx_exception_stubs.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx/cxx_guards.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/driver/libdriver.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/adc_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/adc_dma_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/dac_common_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/esp32/dac_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/timer_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/i2c/i2c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/i2s_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/mcpwm_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/pcnt_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/rmt_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/sigma_delta_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/touch_sensor/touch_sensor_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/touch_sensor/esp32/touch_sensor.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/twai/twai.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver/deprecated/adc_i2s_deprecated.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/efuse/libefuse.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/esp32/esp_efuse_table.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/esp32/esp_efuse_fields.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/esp32/esp_efuse_utility.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/src/esp_efuse_api.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/src/esp_efuse_fields.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/src/esp_efuse_utility.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls/esp_tls.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls/esp_tls_error_capture.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls/esp_tls_platform_port.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/adc_oneshot.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/adc_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/adc_cali.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/adc_cali_curve_fitting.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/adc_continuous.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/esp32/adc_dma.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/esp32/adc_cali_line_fitting.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc/deprecated/esp32/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex/esp32/esp_coex_adapter.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex/src/coexist_debug_diagram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_common/libesp_common.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam/esp_cam_ctlr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac", "type": "LIBRARY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_dac/libesp_driver_dac.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/dac_oneshot.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/dac_cosine.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/dac_continuous.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/dac_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac/esp32/dac_dma.c"], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio/src/gpio.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio/src/rtc_io.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer/src/gptimer.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c/i2c_master.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c/i2c_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_std.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_pdm.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s/i2s_platform.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_com.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_fault.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_gen.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_oper.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_sync.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt/src/rmt_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt/src/rmt_encoder.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt/src/rmt_rx.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio", "type": "LIBRARY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_sdio/libesp_driver_sdio.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio/src/sdio_slave.c"], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi/src/sdspi_crc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi/src/sdspi_host.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_master.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi/src/gpspi/spi_dma.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart/src/uart.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/esp_eth.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/esp_eth_netif_glue.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/mac/esp_eth_mac_esp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/mac/esp_eth_mac_esp_dma.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/mac/esp_eth_mac_esp_gpio.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/phy/esp_eth_phy_generic.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/phy/esp_eth_phy_dp83848.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/phy/esp_eth_phy_ip101.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/phy/esp_eth_phy_ksz80xx.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/phy/esp_eth_phy_lan87xx.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth/src/phy/esp_eth_phy_rtl8201.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_event/libesp_event.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event/default_event_loop.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event/esp_event.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub/src/gdbstub.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub/src/gdbstub_transport.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub/src/packet.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid/src/esp_hidd.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid/src/esp_hidh.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid/src/esp_hid_common.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client/esp_http_client.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client/lib/http_auth.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client/lib/http_header.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/src/httpd_main.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/src/httpd_parse.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/src/httpd_sess.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/src/httpd_txrx.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/src/httpd_uri.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/src/httpd_ws.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/cpu.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/esp_cpu_intr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/esp_memory_utils.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/cpu_region_protect.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/esp_clk.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/clk_ctrl_os.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/hw_random.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/intr_alloc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/mac_addr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/periph_ctrl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/revision.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/rtc_module.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sleep_modem.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sleep_modes.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sleep_console.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sleep_usb.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sleep_gpio.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sleep_event.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/regi2c_ctrl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/esp_gpio_reserve.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sar_periph_ctrl_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/io_mux.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/esp_clk_tree.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp_clk_tree_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/dma/esp_dma_utils.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/dma/gdma_link.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/spi_share_hw_ctrl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/spi_bus_lock.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/clk_utils.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/adc_share_hw_ctrl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/rtc_wdt.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/mspi_timing_tuning.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sleep_wake_stub.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/esp_clock_output.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/rtc_clk.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/rtc_clk_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/rtc_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/rtc_sleep.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/rtc_time.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/chip_info.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/cache_sram_mmu.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/port/esp32/sar_periph_ctrl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/lowpower"], "include_dirs": ["include", "include/soc", "include/soc/esp32", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_io.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_st7789.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/src/esp_lcd_panel_ops.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd/i80/esp_lcd_panel_io_i2s.c"], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm/esp_mmu_map.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm/port/esp32/ext_mem_layout.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm/esp_cache.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm/cache_esp32.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/esp_netif_handlers.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/esp_netif_objects.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/esp_netif_defaults.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/lwip/esp_netif_lwip.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/lwip/esp_netif_sntp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/lwip/netif/wlanif.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/lwip/netif/ethernetif.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition/partition.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/src/phy_override.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/src/lib_printf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/src/phy_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/src/phy_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/esp32/phy_init_data.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/pm_locks.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/pm_trace.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram", "type": "CONFIG_ONLY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm", "bootloader_support", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_sys.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_print.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_crc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_uart.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_efuse.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_gpio.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S"], "include_dirs": ["include", "esp32/include", "esp32/include/esp32", "esp32"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security", "type": "LIBRARY", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_security/libesp_security.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security/src/init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security/src/esp_crypto_lock.c"], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_system/libesp_system.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/esp_err.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/crosscore_int.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/esp_ipc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/freertos_hooks.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/int_wdt.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/panic.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/esp_system.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/startup.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/startup_funcs.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/system_time.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/stack_check.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/ubsan.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/xt_wdt.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/task_wdt/task_wdt.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/cpu_start.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/panic_handler.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/esp_system_chip.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/image_process.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/brownout.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/esp_ipc_isr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/panic_arch.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/debug_stubs.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/arch/xtensa/trax.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/soc/esp32/highint_hdl.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/soc/esp32/clk.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/soc/esp32/reset_reason.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/soc/esp32/system_internal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/port/soc/esp32/cache_err_int.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/src/esp_timer.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/src/esp_timer_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/src/ets_timer_legacy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/src/system_time.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/src/esp_timer_impl_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer/src/esp_timer_impl_lac.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/src/lib_printf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/src/mesh_event.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/src/smartconfig.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/src/wifi_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/src/wifi_default.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/src/wifi_netif.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/src/wifi_default_ap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/esp32/esp_adapter.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/core_dump_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/core_dump_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/core_dump_flash.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/core_dump_uart.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/core_dump_elf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/core_dump_binary.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/core_dump_sha.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/core_dump_crc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/fatfs/libfatfs.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/diskio/diskio.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/diskio/diskio_rawflash.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/diskio/diskio_wl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/src/ff.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/src/ffunicode.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/port/freertos/ffsystem.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/diskio/diskio_sdmmc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/vfs/vfs_fat.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/vfs/vfs_fat_sdmmc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/freertos/libfreertos.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/heap_idf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/app_startup.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/port_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/port_systick.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/list.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/queue.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/tasks.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/timers.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/event_groups.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/esp_additions/freertos_compatibility.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/esp_additions/idf_additions_event_groups.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/hal/libhal.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/hal_utils.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/mpu_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/efuse_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/esp32/efuse_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/wdt_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/mmu_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/esp32/cache_hal_esp32.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/color_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/spi_flash_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/spi_flash_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/spi_flash_encrypt_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/esp32/clk_tree_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/uart_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/uart_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/gpio_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/rtc_io_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/timer_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/ledc_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/ledc_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/i2c_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/i2c_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/rmt_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/pcnt_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/mcpwm_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/twai_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/twai_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/i2s_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/sdm_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/sdmmc_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/emac_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/adc_hal_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/adc_oneshot_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/adc_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/mpi_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/sha_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/aes_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/brownout_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/spi_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/spi_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/spi_slave_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/spi_slave_hal_iram.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/sdio_slave_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/esp32/touch_sensor_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/touch_sensor_hal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/esp32/gpio_hal_workaround.c"], "include_dirs": ["platform_port/include", "esp32/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/heap/libheap.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/heap_caps_base.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/heap_caps.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/heap_caps_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/multi_heap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/tlsf/tlsf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/port/memory_layout_utils.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap/port/esp32/memory_layout.c"], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/json/libjson.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json/cJSON/cJSON.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/log/liblog.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/os/log_timestamp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/log_timestamp_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/os/log_lock.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/os/log_write.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/buffer/log_buffers.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/util.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/log_level/log_level.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/log_level/tag_log_level/tag_log_level.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/log_level/tag_log_level/linked_list/log_linked_list.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log/src/log_level/tag_log_level/cache/log_binary_heap.c"], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/lwip/liblwip.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/apps/sntp/sntp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/api_lib.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/api_msg.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/err.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/if_api.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/netbuf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/netdb.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/netifapi.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/sockets.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/api/tcpip.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/apps/sntp/sntp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/def.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/dns.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/inet_chksum.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ip.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/mem.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/memp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/netif.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/pbuf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/raw.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/stats.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/sys.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/tcp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/tcp_in.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/tcp_out.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/timeouts.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/udp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/autoip.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/dhcp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/etharp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/icmp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/igmp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/dhcp6.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/ethip6.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/icmp6.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/inet6.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/mld6.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/core/ipv6/nd6.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ethernet.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/bridgeif.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/bridgeif_fdb.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/slipif.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/auth.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ccp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/chap-md5.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/chap-new.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/chap_ms.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/demand.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/eap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ecp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/eui64.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/fsm.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ipcp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/lcp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/magic.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/mppe.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/multilink.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/ppp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppapi.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppoe.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/pppos.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/upap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/utils.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/vj.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/hooks/tcp_isn_default.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/hooks/lwip_default_hooks.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/debug/lwip_debug.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/sockets_ext.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/freertos/sys_arch.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/acd_dhcp_check.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/port/esp32xx/vfs_lwip.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/apps/ping/esp_ping.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/apps/ping/ping.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/apps/ping/ping_sock.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/main/libmain.a", "sources": ["C:/Users/<USER>/Desktop/vscode/softap/softap_sta/main/softap_sta.c", "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/main/filters/iir_filter.c"], "include_dirs": [".", "filters"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/mqtt/libmqtt.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt/esp-mqtt/mqtt_client.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/newlib/libnewlib.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/abort.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/assert.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/heap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/flockfile.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/locks.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/poll.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/pthread.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/random.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/getentropy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/reent_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/newlib_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/syscalls.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/termios.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/stdatomic.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/time.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/sysconf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/realpath.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/scandir.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_api.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_cxx_api.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_item_hash_list.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_page.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_pagemanager.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_storage.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_handle_simple.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_handle_locked.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_partition.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_partition_lookup.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_partition_manager.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_types.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_platform.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_bootloader.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash/src/nvs_encrypted_partition.cpp"], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/perfmon/libperfmon.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon/xtensa_perfmon_access.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon/xtensa_perfmon_apis.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/protocomm/libprotocomm.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/src/common/protocomm.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/proto-c/constants.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/proto-c/sec0.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/proto-c/sec1.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/proto-c/sec2.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/proto-c/session.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/src/transports/protocomm_console.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/src/transports/protocomm_httpd.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/src/security/security0.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/src/security/security1.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/src/security/security2.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/pthread/libpthread.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/pthread.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/pthread_cond_var.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/pthread_local_storage.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/pthread_rwlock.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt", "type": "LIBRARY", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/rt/librt.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt/FreeRTOS_POSIX_mqueue.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt/FreeRTOS_POSIX_utils.c"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/sdmmc_cmd.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/sdmmc_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/sdmmc_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/sdmmc_io.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/sdmmc_mmc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/sdmmc_sd.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/soc/libsoc.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/lldesc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/dport_access_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/interrupts.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/gpio_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/uart_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/dport_access.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/adc_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/emac_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/spi_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/ledc_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/pcnt_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/rmt_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/sdm_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/i2s_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/i2c_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/timer_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/lcd_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/mcpwm_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/mpi_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/sdmmc_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/touch_sensor_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/twai_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/wdt_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/dac_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/rtc_io_periph.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32/sdio_slave_periph.c"], "include_dirs": ["include", "esp32", "esp32/include", "esp32/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/flash_brownout_hook.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_drivers.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_generic.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_issi.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_mxic.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_gd.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_winbond.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_boya.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_mxic_opi.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_chip_th.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/memspi_host_driver.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/cache_utils.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/flash_mmap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/flash_ops.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_wrap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/esp_flash_api.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/esp_flash_spi_init.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_os_func_app.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/spiffs/libspiffs.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/spiffs_api.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_cache.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_check.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_gc.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_hydrogen.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/spiffs/src/spiffs_nucleus.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport/transport.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport/transport_ssl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport/transport_internal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport/transport_socks_proxy.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/unity/libunity.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/unity/src/unity.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/unity_compat.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/unity_runner.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/unity_utils_freertos.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/unity_utils_cache.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/unity_utils_memory.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/unity_port_esp32.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb", "type": "CONFIG_ONLY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/vfs/libvfs.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs/vfs.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs/vfs_eventfd.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs/vfs_semihost.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs/nullfs.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/Partition.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/SPI_Flash.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/WL_Ext_Perf.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/WL_Ext_Safe.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/WL_Flash.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/crc32.cpp", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/src/wifi_config.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/src/wifi_scan.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/src/wifi_ctrl.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/src/manager.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/src/handlers.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/src/scheme_console.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning/src/scheme_softap.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/port/os_xtensa.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/port/eloop.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/ap_config.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/ieee802_1x.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/wpa_auth.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/sta_info.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/ieee802_11.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/ap/comeback_token.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/common/sae.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/common/dragonfly.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/common/wpa_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/bitfield.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-siv.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha256-kdf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/ccmp.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-gcm.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/crypto_ops.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/dh_group5.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/dh_groups.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/ms_funcs.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha256-prf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha1-prf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha384-prf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/md4-internal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/sha1-tprf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/common/ieee802_11_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/chap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/mschapv2.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/base64.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/ext_password.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/uuid.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/wpabuf.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/wpa_debug.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/utils/json.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_build.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_parse.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_attr_process.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_dev_attr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/wps/wps_enrollee.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/common/sae_pk.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/rc4.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/des-internal.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-wrap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-unwrap.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/esp-idf/xtensa/libxtensa.a", "sources": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/eri.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/xt_trax.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/xtensa_context.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/xtensa_intr_asm.S", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/xtensa_intr.c", "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm", "bootloader_support", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32/include", "esp32/include/esp32", "esp32"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32", "esp32/include", "esp32/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/touch_element", "lib": "__idf_touch_element", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/main", "lib": "__idf_main", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": [".", "filters"]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/gdbinit/symbols", "02_prefix_map": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/gdbinit/prefix_map", "03_py_extensions": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/gdbinit/py_extensions", "04_connect": "C:/Users/<USER>/Desktop/vscode/softap/softap_sta/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32-wrover-kit-3.3v.cfg"}